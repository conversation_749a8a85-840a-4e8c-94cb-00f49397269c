# MongoDB Laboratory Databases - BDSL456B

This repository contains separate database collections for each program in the MongoDB Laboratory Manual (BDSL456B). Each JSON file contains the exact sample data mentioned in the HTML manual.

## Database Structure

### Program 1: Where Clause, AND, OR Operations
- **Database**: Students
- **Collection**: details
- **File**: `Program1_Students_Details_Collection.json`
- **Purpose**: Practice WHERE clause, AND, OR operations, basic CRUD operations

### Program 2: Field Selection and Limiting
- **Database**: Students
- **Collection**: details
- **File**: `Program2_Students_Details_Collection.json`
- **Purpose**: Practice field projection and limit operations

### Program 3: Query Selectors (Part A Only)
- **Database**: QuerySelectors
- **Collections**:
  - customers (`Program3_Customers_Collection.json`)
  - places (`Program3_Places_Collection.json`) - for geospatial queries (if needed)
- **Purpose**: Practice comparison selectors, logical selectors
- **Note**: Part B (Geospatial and Bitwise selectors) excluded as requested

### Program 4: Projection Operators
- **Database**: ProjectionDB
- **Collection**: students
- **File**: `Program4_Students_Collection.json`
- **Purpose**: Practice $, $elemMatch, and $slice operators

### Program 5: Aggregation Operations
- **Database**: AggregationDB
- **Collection**: students
- **File**: `Program5_Students_Aggregation_Collection.json`
- **Purpose**: Practice $avg, $min, $max, $push, $addToSet operators

### Program 6: Aggregation Pipeline
- **Database**: AggregationPipelineDB
- **Collection**: students
- **File**: `Program6_Students_Aggregation_Collection.json`
- **Purpose**: Practice $match, $group, $sort, $project, $skip operations

### Program 7: Real-world Collections
- **Database**: RealWorldDB
- **Collections**:
  - listingsAndReviews (`Program7_Airbnb_ListingsAndReviews_Collection.json`)
  - reviews (`Program7_Ecommerce_Reviews_Collection.json`)
- **Purpose**: Work with complex real-world data structures

### Program 8: Indexing
- **Database**: IndexingDB
- **Collection**: users
- **File**: `Program8_Users_Collection.json`
- **Purpose**: Practice creating different types of indexes

### Program 9: Text Search
- **Database**: TextSearchDB
- **Collections**:
  - products (`Program9_Products_Collection.json`)
  - articles (`Program9_Articles_Collection.json`)
- **Purpose**: Practice text search queries and exclusions

### Program 10: Text Search with Aggregation
- **Database**: TextDB
- **Collection**: catalog
- **File**: `Program10_Catalog_Collection.json`
- **Purpose**: Practice aggregation pipeline with text search

## How to Import into MongoDB Compass

### Method 1: Using MongoDB Compass GUI

1. **Open MongoDB Compass**
2. **Connect to your MongoDB instance**
3. **Create a new database**:
   - Click "Create Database"
   - Enter database name (e.g., "Students", "QuerySelectors", etc.)
   - Enter collection name (e.g., "details", "customers", etc.)
4. **Import data**:
   - Click on the collection
   - Click "ADD DATA" → "Import JSON or CSV file"
   - Select the corresponding JSON file
   - Click "Import"

### Method 2: Using MongoDB Shell

```bash
# Connect to MongoDB
mongosh

# Create and switch to database
use Students

# Import collection (run this in your terminal, not in mongosh)
mongoimport --db Students --collection details --file Program1_Students_Details_Collection.json --jsonArray

# Repeat for other databases and collections
use QuerySelectors
mongoimport --db QuerySelectors --collection customers --file Program3_Customers_Collection.json --jsonArray
mongoimport --db QuerySelectors --collection places --file Program3_Places_Collection.json --jsonArray


# Continue for all other collections...
```

## Quick Setup Commands

```bash
# Import all collections at once (run in terminal)
mongoimport --db Students --collection details --file Program1_Students_Details_Collection.json --jsonArray
mongoimport --db QuerySelectors --collection customers --file Program3_Customers_Collection.json --jsonArray
mongoimport --db QuerySelectors --collection places --file Program3_Places_Collection.json --jsonArray

mongoimport --db ProjectionDB --collection students --file Program4_Students_Collection.json --jsonArray
mongoimport --db AggregationDB --collection students --file Program5_Students_Aggregation_Collection.json --jsonArray
mongoimport --db RealWorldDB --collection listingsAndReviews --file Program7_Airbnb_ListingsAndReviews_Collection.json --jsonArray
mongoimport --db RealWorldDB --collection reviews --file Program7_Ecommerce_Reviews_Collection.json --jsonArray
mongoimport --db IndexingDB --collection users --file Program8_Users_Collection.json --jsonArray
mongoimport --db TextSearchDB --collection products --file Program9_Products_Collection.json --jsonArray
mongoimport --db TextSearchDB --collection articles --file Program9_Articles_Collection.json --jsonArray
mongoimport --db TextDB --collection catalog --file Program10_Catalog_Collection.json --jsonArray
```

## Notes

- All data is based on the exact examples provided in the BDSL456B MongoDB Laboratory Manual
- Each collection is designed to support the specific operations mentioned in each program
- Some programs share the same database/collection as they build upon previous exercises
- Remember to create appropriate indexes as mentioned in the manual for optimal query performance

## Practice Suggestions

1. Start with Program 1 to understand basic operations
2. Progress through each program sequentially
3. Try variations of the queries mentioned in the manual
4. Experiment with different query combinations
5. Use MongoDB Compass's query builder to visualize your queries

Happy Learning! 🚀
